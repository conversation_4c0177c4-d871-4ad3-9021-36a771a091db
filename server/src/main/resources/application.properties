# 服务器配置
server.port=8082
server.servlet.context-path=/

# 应用配置
spring.application.name=ai-textbook-server

# 编码配置
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true

# 日志配置
logging.level.com.goodlab=DEBUG
logging.level.org.springframework=INFO
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n

# JSON配置
spring.jackson.default-property-inclusion=NON_NULL
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8

# 数据源配置
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=**************************************************************************************************
spring.datasource.username=root
spring.datasource.password=root123456

# # 数据源配置
# spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
# spring.datasource.url=***************************************
# spring.datasource.username=ai_platform_user
# spring.datasource.password=ai_platform_2024!


# MyBatis 配置
mybatis.configuration.map-underscore-to-camel-case=true
